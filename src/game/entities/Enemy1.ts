import { Enemy, EnemyConfig } from "./Enemy";
import { BulletManager } from "../systems/BulletManager";
import { ResolutionManager } from "../systems/ResolutionManager";
import { GameConfig } from "../config/GameConfig";

/**
 * Enemy Type 1: Slow Downward Mover
 * - Moves only downward at a slow speed
 * - Has a slow shooting rate
 * - Simple, predictable movement pattern
 */
class Enemy1 extends Enemy {
  constructor(
    scene: Phaser.Scene,
    x?: number,
    y?: number,
    config: EnemyConfig = {},
    bulletManager: BulletManager | null = null
  ) {
    // Set default configuration for Enemy1
    const scaleFactor = ResolutionManager.getInstance().getScaleFactor();
    const globalDownwardSpeed =
      GameConfig.getInstance().getGlobalDownwardSpeed();
    const enemy1Config: EnemyConfig = {
      maxHealth: 1,
      color: 0xff4444, // Light red
      size: 0.8,
      speed: globalDownwardSpeed * scaleFactor, // Use global downward speed
      turnSpeed: 0, // No turning needed for downward movement
      fireRate: 3000, // Slower shooting (3 seconds)
      shootInFacingDirection: true,
      scoreValue: 50, // Lower score value
      ...config, // Allow overrides
    };

    // Call parent constructor with enemy1 texture
    super(scene, x, y, enemy1Config, bulletManager, "enemy1");
  }

  protected initializeMovement(): void {
    // Only move downward at the configured speed
    this.setVelocity(0, this.speed);

    // Face downward (0 rotation = facing down)
    this.rotation = 0;
  }

  protected updateMovement(
    time: number,
    delta: number,
    playerPosition?: { x: number; y: number }
  ): void {
    // Enemy1 only moves downward - no complex movement logic needed
    // Just maintain downward velocity
    this.setVelocity(0, this.speed);
    this.rotation = 0; // Always face downward
  }

  protected updateShooting(
    time: number,
    playerPosition?: { x: number; y: number }
  ): void {
    if (!playerPosition || !this.bulletManager) return;

    if (time > this.nextFireTime) {
      // Always shoot straight down
      const direction = 0; // Downward direction
      this.shoot(direction);
      this.nextFireTime = time + this.fireRate;
    }
  }
}

export { Enemy1 };
